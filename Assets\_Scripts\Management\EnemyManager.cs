using UnityEngine;
using System.Collections.Generic;
using Unity.Mathematics;
using Unity.Collections;
using Unity.Jobs;
using System.Linq;
using Pathfinding;
using SonicBloom.Koreo;
using Chronos;
using System;
using BTR;
using BTR.Utilities;  // For PooledList
using BTR.EnemySystem.Core;  // For SpatialGrid
using BTR.EnemySystem.Services;  // For CentralizedPathfindingService
using BTR.EnemySystem.Entities;  // For IEntity, ICombatEntity

namespace BTR
{
    // Add reference to EnemyAudioManager
    [RequireComponent(typeof(EnemyAudioManager))]
    public class EnemyManager : MonoBehaviour
    {
        public static EnemyManager Instance { get; private set; }

        // Legacy enemy system
        public List<EnemyCore> Enemies { get; private set; } = new List<EnemyCore>();

        // New entity system
        public List<ICombatEntity> CombatEntities { get; private set; } = new List<ICombatEntity>();

        // Other systems
        private List<StaticShooter> staticEnemies = new List<StaticShooter>();
        private List<Transform> _queryResults = new List<Transform>();

        // Spatial Grid System
        private SpatialGrid _enemyGrid;
        [SerializeField] private float _gridCellSize = 5f;
        [SerializeField] private int _maxGridCells = 1000;

        [SerializeField]
        private LayerMask obstacleLayerMask;
        private const int maxRaycastsPerJob = 32;

        [SerializeField, EventID]
        private string unlockEventID = "EnemyShootUnlocked";

        [SerializeField]
        private EnemyEventChannel enemyEventChannel;

        [SerializeField, EventID]
        private string lockEventID = "EnemyShootLocked";

        [Header("Basic Enemy Shooting")]
        [SerializeField] private float minEnemiesPerShot = 3f;
        [SerializeField] private float maxEnemiesPerShot = 5f;
        [SerializeField] private float shotGroupDelay = 0.2f;

        private Timeline managerTimeline;
        private PlayerLocking playerLocking;
        private bool previousLockLoopState;
        private bool isInLockLoop;

        [SerializeField] private bool enableDebugLogs = true;

        [Header("Centralized Pathfinding")]
        [SerializeField] private bool enableCentralizedPathfinding = true;
        [SerializeField] private CentralizedPathfindingService pathfindingService;
        [SerializeField] private bool showPathfindingStats = true;

        [Header("Koreographer Events")]
        [SerializeField, EventID]
        private string groupEnemyShootingEventID = "Group Enemy Shooting";

        private bool isGroupShooting = false;
        private bool isProcessingBasicEnemies = false;

        [SerializeField] private bool enableBasicEnemyShootingLogs = true;

        [SerializeField] private float lineOfSightCheckInterval = 0.1f;
        [SerializeField] private float raycastOriginOffset = 1f;
        [SerializeField] private float raycastTargetOffset = 1f;

        private Transform playerTransform;
        private float nextLineOfSightCheck;
        private NativeArray<RaycastCommand> commands;
        private NativeArray<RaycastHit> results;
        private bool isInitialized;

        private bool debugLogging = false;
        private int lastEventSample = -1;

        // Wave-specific fields
        private bool _isWaveActive;
        private int _currentWaveNumber;
        private float _lastGridUpdateTime;
        private const float GRID_UPDATE_INTERVAL = 0.1f; // 10 updates per second

        private HashSet<EnemyCore> pendingCleanup = new HashSet<EnemyCore>();

        private Dictionary<EnemyCore, IFireRateLimiter> enemyRateLimiters = new Dictionary<EnemyCore, IFireRateLimiter>();
        private Dictionary<ICombatEntity, IFireRateLimiter> entityRateLimiters = new Dictionary<ICombatEntity, IFireRateLimiter>();

        // Legacy EnemyCore rate limiting
        public void RegisterEnemyRateLimiter(EnemyCore enemy, IFireRateLimiter limiter)
        {
            if (!enemyRateLimiters.ContainsKey(enemy))
            {
                enemyRateLimiters[enemy] = limiter;
            }
        }

        public void UnregisterEnemyRateLimiter(EnemyCore enemy)
        {
            if (enemyRateLimiters.ContainsKey(enemy))
            {
                enemyRateLimiters.Remove(enemy);
            }
        }

        public bool CanEnemyFire(EnemyCore enemy)
        {
            if (enemyRateLimiters.TryGetValue(enemy, out var limiter))
            {
                return limiter.CanFire();
            }
            return true; // No limiter = no restriction
        }

        public void OnEnemyFired(EnemyCore enemy)
        {
            if (enemyRateLimiters.TryGetValue(enemy, out var limiter))
            {
                limiter.OnFired();
            }
        }

        // New entity system rate limiting
        public void RegisterEntityRateLimiter(ICombatEntity entity, IFireRateLimiter limiter)
        {
            if (!entityRateLimiters.ContainsKey(entity))
            {
                entityRateLimiters[entity] = limiter;
            }
        }

        public void UnregisterEntityRateLimiter(ICombatEntity entity)
        {
            if (entityRateLimiters.ContainsKey(entity))
            {
                entityRateLimiters.Remove(entity);
            }
        }

        public bool CanEntityFire(ICombatEntity entity)
        {
            if (entityRateLimiters.TryGetValue(entity, out var limiter))
            {
                return limiter.CanFire();
            }
            return true; // No limiter = no restriction
        }

        public void OnEntityFired(ICombatEntity entity)
        {
            if (entityRateLimiters.TryGetValue(entity, out var limiter))
            {
                limiter.OnFired();
            }
        }

        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeComponents();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void InitializeComponents()
        {
            Enemies = new List<EnemyCore>();
            CombatEntities = new List<ICombatEntity>();
            staticEnemies = new List<StaticShooter>();
            _queryResults = new List<Transform>();

            // Initialize spatial grid
            _enemyGrid = gameObject.AddComponent<SpatialGrid>();
            _enemyGrid.CellSize = _gridCellSize;
            _enemyGrid.MaxCells = _maxGridCells;
            _enemyGrid.Initialize(transform.position);

            // Initialize centralized pathfinding service
            InitializeCentralizedPathfinding();

            RegisterKoreographerEvents();
        }

        private void InitializeCentralizedPathfinding()
        {
            if (enableCentralizedPathfinding)
            {
                // Create pathfinding service if not assigned
                if (pathfindingService == null)
                {
                    var pathfindingGO = new GameObject("CentralizedPathfindingService");
                    pathfindingGO.transform.SetParent(transform);
                    pathfindingService = pathfindingGO.AddComponent<CentralizedPathfindingService>();
                }

                if (enableDebugLogs)
                {
                    Debug.Log($"[{GetType().Name}] Centralized pathfinding initialized");
                }
            }
        }

        private void Start()
        {
            InitializePlayerLocking();
            InitializeLineOfSightSystem();
        }

        private void InitializePlayerLocking()
        {
            // Wait a frame to ensure PlayerLocking is initialized
            StartCoroutine(InitializePlayerLockingDelayed());
        }

        private System.Collections.IEnumerator InitializePlayerLockingDelayed()
        {
            yield return null; // Wait one frame

            playerLocking = PlayerLocking.Instance;
            if (playerLocking == null)
            {
                Debug.LogWarning($"[{GetType().Name}] Could not find PlayerLocking instance, will retry next frame");
                yield return null; // Wait another frame
                playerLocking = PlayerLocking.Instance;

                if (playerLocking == null)
                {
                    if (enableDebugLogs)
                        Debug.LogError($"[{GetType().Name}] Could not find PlayerLocking instance after retry");
                    yield break;
                }
            }

            previousLockLoopState = false;
            if (enableDebugLogs)
                Debug.Log($"[{GetType().Name}] PlayerLocking initialized successfully");
        }

        private void RegisterKoreographerEvents()
        {
            if (Koreographer.Instance == null)
            {
                Debug.LogWarning($"[{GetType().Name}] Koreographer.Instance not found");
                return;
            }

            try
            {
                Koreographer.Instance.RegisterForEvents(groupEnemyShootingEventID, OnGroupEnemyShooting);
                Koreographer.Instance.RegisterForEvents(unlockEventID, OnMusicalEnemyShootUnlocked);
                Koreographer.Instance.RegisterForEvents(lockEventID, OnMusicalEnemyShootLocked);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[EnemyManager] Error registering Koreographer events: {e.Message}");
            }
        }

        private void OnEnable()
        {
            RegisterKoreographerEvents();
        }

        private void OnDisable()
        {
            if (Koreographer.Instance != null)
            {
                Koreographer.Instance.UnregisterForEvents(groupEnemyShootingEventID, OnGroupEnemyShooting);
                Koreographer.Instance.UnregisterForEvents(unlockEventID, OnMusicalEnemyShootUnlocked);
                Koreographer.Instance.UnregisterForEvents(lockEventID, OnMusicalEnemyShootLocked);
            }
        }

        // Legacy EnemyCore registration
        public void RegisterEnemy(EnemyCore enemy)
        {
            if (!Enemies.Contains(enemy))
            {
                Enemies.Add(enemy);
                _enemyGrid.Add(enemy.transform, enemy.transform.position);
                EnemyPathManager.Instance?.RegisterEnemy(enemy);
            }
        }

        public void UnregisterEnemy(EnemyCore enemy)
        {
            Enemies.Remove(enemy);
            // Grid automatically handles cleanup in its OnDestroy
            EnemyPathManager.Instance?.UnregisterEnemy(enemy);
        }

        // New entity system registration
        public void RegisterCombatEntity(ICombatEntity entity)
        {
            if (!CombatEntities.Contains(entity))
            {
                CombatEntities.Add(entity);
                _enemyGrid.Add(entity.Transform, entity.Transform.position);
                EnemyPathManager.Instance?.RegisterEntity(entity);

                if (enableDebugLogs)
                {
                    Debug.Log($"[EnemyManager] Registered new combat entity: {entity.GameObject.name}");
                }
            }
        }

        public void UnregisterCombatEntity(ICombatEntity entity)
        {
            CombatEntities.Remove(entity);
            // Grid automatically handles cleanup in its OnDestroy
            EnemyPathManager.Instance?.UnregisterEntity(entity);

            if (enableDebugLogs)
            {
                Debug.Log($"[EnemyManager] Unregistered combat entity: {entity.GameObject.name}");
            }
        }

        public void RegisterStaticEnemyShooting(StaticShooter enemy)
        {
            if (!staticEnemies.Contains(enemy))
            {
                staticEnemies.Add(enemy);
            }
        }

        public void UnregisterStaticEnemyShooting(StaticShooter enemy)
        {
            staticEnemies.Remove(enemy);
        }

        public float GetCurrentTime()
        {
            return Time.time;
        }

        private void Update()
        {
            if (!_isWaveActive) return;

            // Throttle grid updates for performance
            if (Time.time >= _lastGridUpdateTime + GRID_UPDATE_INTERVAL)
            {
                UpdateEnemyPositions();
                _lastGridUpdateTime = Time.time;
            }

            if (Time.time >= nextLineOfSightCheck)
            {
                CheckLineOfSightBatched();
                nextLineOfSightCheck = Time.time + lineOfSightCheckInterval;
            }

            // Update lock state
            if (playerLocking != null)
            {
                bool newLockState = playerLocking.GetLockedProjectileCount() > 0;

                if (newLockState != isInLockLoop)
                {
                    isInLockLoop = newLockState;
                    previousLockLoopState = isInLockLoop;
                }
            }

#if !UNITY_EDITOR
            enableDebugLogs = false;
#endif
        }

        private void UpdateEnemyPositions()
        {
            // Update legacy enemies
            foreach (var enemy in Enemies.Where(e => e != null))
            {
                _enemyGrid.Add(enemy.transform, enemy.transform.position);
            }

            // Update new entities
            foreach (var entity in CombatEntities.Where(e => e != null && e.IsAlive))
            {
                _enemyGrid.Add(entity.Transform, entity.Transform.position);
            }
        }

        private void OnMusicalEnemyShoot(KoreographyEvent evt, bool isLocked)
        {
            if (isGroupShooting)
            {
                if (enableDebugLogs)
                {
                    Debug.Log("[EnemyManager] Skipping basic enemy shooting during group shot");
                }
                return;
            }

            try
            {
                Enemies.RemoveAll(x => x == null);
                ProcessBasicEnemyShooting();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[EnemyManager] Error in OnMusicalEnemyShoot: {e.Message}");
            }
        }

        private void ProcessBasicEnemyShooting()
        {
            if (isProcessingBasicEnemies) return;

            isProcessingBasicEnemies = true;
            try
            {
                // Clean up null references
                Enemies.RemoveAll(x => x == null || !x.gameObject.activeInHierarchy);
                CombatEntities.RemoveAll(x => x == null || !x.IsAlive || !x.GameObject.activeInHierarchy);

                // Get available legacy enemies
                var availableLegacyEnemies = Enemies
                    .Where(e => e.gameObject.activeInHierarchy)
                    .Select(e => e.GetComponent<ICombatBehavior>())
                    .Where(combat =>
                    {
                        if (combat == null) return false;
                        var enemyCore = (combat as MonoBehaviour)?.GetComponent<EnemyCore>();
                        return combat.CanAttack && enemyCore != null && CanEnemyFire(enemyCore);
                    })
                    .ToList();

                // Get available new entities with combat strategies
                var availableNewEntities = CombatEntities
                    .Where(e => e.IsAlive && e.GameObject.activeInHierarchy)
                    .Where(e => CanEntityFire(e))
                    .Select(e => new { Entity = e, Strategy = e.GameObject.GetComponent<BTR.EnemySystem.Strategies.Combat.CombatStrategy>() })
                    .Where(x => x.Strategy != null && x.Strategy.CanAttack)
                    .ToList();

                int totalAvailable = availableLegacyEnemies.Count + availableNewEntities.Count;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                if (debugLogging)
                {
                    Debug.Log($"[BasicEnemyShooting] Processing {totalAvailable} ready enemies ({availableLegacyEnemies.Count} legacy + {availableNewEntities.Count} new) out of {Enemies.Count + CombatEntities.Count} total");
                }
#endif

                int enemiesToShoot = Mathf.Min(
                    UnityEngine.Random.Range((int)minEnemiesPerShot, (int)maxEnemiesPerShot + 1),
                    totalAvailable
                );

                if (enemiesToShoot > 0)
                {
                    // Combine both lists for random selection
                    var allAvailable = new List<object>();
                    allAvailable.AddRange(availableLegacyEnemies.Cast<object>());
                    allAvailable.AddRange(availableNewEntities.Cast<object>());

                    var selectedEnemies = allAvailable
                        .OrderBy(x => UnityEngine.Random.value)
                        .Take(enemiesToShoot);

                    foreach (var selected in selectedEnemies)
                    {
                        if (selected is ICombatBehavior legacyCombat)
                        {
                            // Handle legacy enemy
                            var enemyCore = (legacyCombat as MonoBehaviour)?.GetComponent<EnemyCore>();
                            if (enemyCore != null)
                            {
                                legacyCombat.PerformAttack();
                                OnEnemyFired(enemyCore);
                            }
                        }
                        else if (selected is { } newEntity)
                        {
                            // Handle new entity
                            var entityData = newEntity.GetType().GetProperty("Entity")?.GetValue(newEntity) as ICombatEntity;
                            var strategy = newEntity.GetType().GetProperty("Strategy")?.GetValue(newEntity) as BTR.EnemySystem.Strategies.Combat.CombatStrategy;

                            if (entityData != null && strategy != null)
                            {
                                strategy.PerformAttack();
                                OnEntityFired(entityData);
                            }
                        }
                    }
                }
            }
            finally
            {
                isProcessingBasicEnemies = false;
            }
        }

        private void OnMusicalEnemyShootUnlocked(KoreographyEvent evt)
        {
            if (debugLogging && evt.StartSample != lastEventSample)
            {
                Debug.Log($"[EnemyManager] OnMusicalEnemyShootUnlocked triggered - Event: {evt.StartSample}");
                lastEventSample = evt.StartSample;
            }
            OnMusicalEnemyShoot(evt, false);
        }

        private void OnMusicalEnemyShootLocked(KoreographyEvent evt)
        {
            if (debugLogging && evt.StartSample != lastEventSample)
            {
                Debug.Log($"[EnemyManager] OnMusicalEnemyShootLocked triggered - Event: {evt.StartSample}");
                lastEventSample = evt.StartSample;
            }
            OnMusicalEnemyShoot(evt, true);
        }

        private void InitializeLineOfSightSystem()
        {
            playerTransform = GameObject.FindGameObjectWithTag("Player")?.transform;
            if (playerTransform == null)
            {
                Debug.LogError("EnemyManager: Player not found!");
                return;
            }

            int maxEnemies = Mathf.Max(100, Enemies.Capacity);
            commands = new NativeArray<RaycastCommand>(maxEnemies, Allocator.Persistent);
            results = new NativeArray<RaycastHit>(maxEnemies, Allocator.Persistent);
            isInitialized = true;
        }

        private void CheckLineOfSightBatched()
        {
            if (!isInitialized || playerTransform == null) return;

            try
            {
                Vector3 playerPos = playerTransform.position + Vector3.up * raycastTargetOffset;
                int validEnemyCount = 0;

                // Use spatial grid to get only nearby enemies
                var nearbyEnemies = GetEnemiesInRadius(playerPos, 50f);
                if (nearbyEnemies.Count == 0) return;

                const int BATCH_SIZE = 32;
                int totalBatches = Mathf.CeilToInt(nearbyEnemies.Count / (float)BATCH_SIZE);

                for (int batch = 0; batch < totalBatches; batch++)
                {
                    int startIdx = batch * BATCH_SIZE;
                    int batchCount = Mathf.Min(BATCH_SIZE, nearbyEnemies.Count - startIdx);
                    validEnemyCount = 0;

                    for (int i = 0; i < batchCount; i++)
                    {
                        var enemy = nearbyEnemies[startIdx + i];
                        if (enemy == null || !enemy.gameObject.activeInHierarchy) continue;

                        Vector3 origin = enemy.transform.position + Vector3.up * raycastOriginOffset;
                        Vector3 direction = playerPos - origin;
                        float distance = direction.magnitude;
                        direction.Normalize();

                        var queryParameters = new QueryParameters(
                            obstacleLayerMask,
                            false,
                            QueryTriggerInteraction.Ignore
                        );

                        commands[validEnemyCount] = new RaycastCommand(origin, direction, queryParameters, distance);
                        validEnemyCount++;
                    }

                    if (validEnemyCount == 0) continue;

                    JobHandle handle = RaycastCommand.ScheduleBatch(commands, results, validEnemyCount);
                    handle.Complete();

                    for (int i = 0; i < validEnemyCount; i++)
                    {
                        var enemy = nearbyEnemies[startIdx + i];
                        if (enemy != null && enemy.gameObject.activeInHierarchy)
                        {
                            bool hasLineOfSight = !results[i].collider;
                            enemy.HandleLineOfSightResult(hasLineOfSight);
                        }
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Error in CheckLineOfSightBatched: {e}");
            }
        }

        private void OnGroupEnemyShooting(KoreographyEvent evt)
        {
            isGroupShooting = true;

            try
            {
                staticEnemies.RemoveAll(enemy => enemy == null || !enemy.gameObject.activeInHierarchy);

                Vector3[] shooterPositions = new Vector3[staticEnemies.Count];
                int validShooterCount = 0;

                foreach (var enemy in staticEnemies)
                {
                    if (enemy != null && enemy.gameObject.activeInHierarchy)
                    {
                        enemy.Shoot();
                        shooterPositions[validShooterCount++] = enemy.transform.position;

#if UNITY_EDITOR || DEVELOPMENT_BUILD
                        if (debugLogging)
                        {
                            Debug.Log("Enemy shot fired");
                        }
#endif
                    }
                }

                if (validShooterCount > 0 && ProjectileAudioManager.Instance != null)
                {
                    System.Array.Resize(ref shooterPositions, validShooterCount);
                    ProjectileAudioManager.PlayGroupProjectileSound(shooterPositions);
                }
            }
            finally
            {
                isGroupShooting = false;
            }
        }

        private void OnDestroy()
        {
            if (commands.IsCreated) commands.Dispose();
            if (results.IsCreated) results.Dispose();
            if (_enemyGrid != null)
            {
                Destroy(_enemyGrid);
            }
        }

        public void ShowAllTargetIndicators()
        {
            foreach (var enemy in Enemies)
            {
                if (enemy != null && enemy.gameObject.activeInHierarchy)
                {
                    var indicator = enemy.GetComponent<EnemyTargetIndicator>();
                    if (indicator != null)
                    {
                        indicator.Show();
                    }
                }
            }
        }

        public void HideAllTargetIndicators()
        {
            foreach (var enemy in Enemies)
            {
                if (enemy != null && enemy.gameObject.activeInHierarchy)
                {
                    var indicator = enemy.GetComponent<EnemyTargetIndicator>();
                    if (indicator != null)
                    {
                        indicator.Hide();
                    }
                }
            }
        }

        // New spatial query methods (non-destructive additions)
        public bool HasEnemiesInRadius(Vector3 position, float radius)
        {
            using (var enemies = new PooledList<Transform>())
            {
                _enemyGrid.Query(position, radius, enemies.List);
                return enemies.List.Count > 0;
            }
        }

        public int GetEnemyCountInRadius(Vector3 position, float radius)
        {
            using (var enemies = new PooledList<Transform>())
            {
                _enemyGrid.Query(position, radius, enemies.List);
                return enemies.List.Count;
            }
        }

        public List<EnemyCore> GetEnemiesInRadius(Vector3 position, float radius)
        {
            using (var enemies = new PooledList<Transform>())
            {
                _enemyGrid.Query(position, radius, enemies.List);
                return enemies.List
                    .Select(t => t.GetComponent<EnemyCore>())
                    .Where(e => e != null)
                    .ToList();
            }
        }

        public void OnWaveStart(int waveNumber)
        {
            _isWaveActive = true;
            _currentWaveNumber = waveNumber;
            _lastGridUpdateTime = 0f;

            // Clear and rebuild spatial grid
            foreach (var enemy in Enemies.Where(e => e != null))
            {
                _enemyGrid.Add(enemy.transform, enemy.transform.position);
            }
        }

        public void OnWaveEnd()
        {
            try
            {
                // Clear any remaining legacy enemies
                foreach (var enemy in Enemies.ToList())
                {
                    if (enemy != null)
                    {
                        UnregisterEnemyRateLimiter(enemy);
                        enemy.Die();
                    }
                }

                // Clear any remaining new entities
                foreach (var entity in CombatEntities.ToList())
                {
                    if (entity != null && entity.IsAlive)
                    {
                        UnregisterEntityRateLimiter(entity);
                        // Trigger death through health component
                        var healthComponent = entity.GameObject.GetComponent<BTR.EnemySystem.Components.EnemyHealthComponent>();
                        healthComponent?.TakeDamage(float.MaxValue);
                    }
                }

                // Wait for cleanup to complete
                if (HasPendingCleanup())
                {
                    Debug.Log($"[ENEMY] {pendingCleanup.Count} enemies still cleaning up...");
                    return;
                }

                // Clear all lists
                Enemies.Clear();
                CombatEntities.Clear();
                enemyRateLimiters.Clear();
                entityRateLimiters.Clear();

                // Reinitialize spatial grid
                if (_enemyGrid != null)
                {
                    Destroy(_enemyGrid);
                    _enemyGrid = gameObject.AddComponent<SpatialGrid>();
                    _enemyGrid.CellSize = _gridCellSize;
                    _enemyGrid.MaxCells = _maxGridCells;
                    _enemyGrid.Initialize(transform.position);
                }

                Debug.Log("[ENEMY] Wave end cleanup complete.");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ENEMY] Error during wave end: {e.Message}\n{e.StackTrace}");
            }
        }

        public void GetEnemiesInRadius(Vector3 position, float radius, List<Transform> results)
        {
            if (results == null) return;
            _enemyGrid.Query(position, radius, results);
        }

        public Transform GetClosestEnemy(Vector3 position, float maxRadius)
        {
            _queryResults.Clear();
            GetEnemiesInRadius(position, maxRadius, _queryResults);

            Transform closest = null;
            float closestDistance = float.MaxValue;

            foreach (var enemy in _queryResults)
            {
                if (enemy == null) continue;
                float distance = Vector3.Distance(position, enemy.position);
                if (distance < closestDistance)
                {
                    closestDistance = distance;
                    closest = enemy;
                }
            }

            return closest;
        }

        public bool HasPendingCleanup()
        {
            return pendingCleanup.Count > 0;
        }

        public void RegisterForCleanup(EnemyCore enemy)
        {
            if (enemy != null)
            {
                pendingCleanup.Add(enemy);
            }
        }

        public void UnregisterFromCleanup(EnemyCore enemy)
        {
            if (enemy != null)
            {
                pendingCleanup.Remove(enemy);
            }
        }

        // Centralized Pathfinding API
        /// <summary>
        /// Request a path using the centralized pathfinding system
        /// </summary>
        public void RequestCentralizedPath(BTR.EnemySystem.Entities.IEntity entity, Vector3 targetPosition, System.Action<List<Vector3>> callback)
        {
            if (enableCentralizedPathfinding && pathfindingService != null)
            {
                pathfindingService.RequestPath(entity, targetPosition, callback);
            }
            else if (enableDebugLogs)
            {
                Debug.LogWarning($"[{GetType().Name}] Centralized pathfinding not available - service disabled or missing");
            }
        }

        /// <summary>
        /// Check if centralized pathfinding is available
        /// </summary>
        public bool IsCentralizedPathfindingAvailable()
        {
            return enableCentralizedPathfinding && pathfindingService != null;
        }

        /// <summary>
        /// Get pathfinding performance statistics
        /// </summary>
        public (int hits, int misses, float hitRate, int queueSize) GetPathfindingStats()
        {
            if (pathfindingService != null)
            {
                return pathfindingService.GetPerformanceStats();
            }
            return (0, 0, 0f, 0);
        }

        /// <summary>
        /// Display centralized pathfinding stats in GUI (called by EnemyPathManager)
        /// </summary>
        public void DisplayPathfindingStatsGUI()
        {
            if (showPathfindingStats && enableCentralizedPathfinding && pathfindingService != null)
            {
                var stats = GetPathfindingStats();

                GUILayout.Label("=== Centralized Pathfinding ===");
                GUILayout.Label($"Cache Hits: {stats.hits}");
                GUILayout.Label($"Cache Misses: {stats.misses}");
                GUILayout.Label($"Hit Rate: {stats.hitRate:P1}");
                GUILayout.Label($"Queue Size: {stats.queueSize}");
            }
        }
    }
}
